import { useAuth } from '../context/AuthContext';
import { useLanguageNavigation } from '../hooks/useLanguageNavigation';
import { API_URL } from '../config/env';
import { useState } from 'react';
import axios from 'axios';

const AdminDebugPage = () => {
  const { user, loading } = useAuth();
  const { navigateToHome } = useLanguageNavigation();
  const [testResults, setTestResults] = useState({});

  const testAuthEndpoint = async () => {
    try {
      const response = await axios.get(`${API_URL}/auth/me`, {
        withCredentials: true
      });
      setTestResults(prev => ({
        ...prev,
        authMe: { success: true, data: response.data }
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        authMe: { success: false, error: error.response?.data || error.message }
      }));
    }
  };

  const testAdminEndpoint = async () => {
    try {
      const response = await axios.get(`${API_URL}/moderation/dashboard/stats`, {
        withCredentials: true
      });
      setTestResults(prev => ({
        ...prev,
        adminStats: { success: true, data: response.data }
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        adminStats: { success: false, error: error.response?.data || error.message }
      }));
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Admin Access Debug Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* User Info */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">User Information</h2>
            <div className="space-y-2">
              <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
              <p><strong>User exists:</strong> {user ? 'Yes' : 'No'}</p>
              {user && (
                <>
                  <p><strong>Username:</strong> {user.username}</p>
                  <p><strong>Email:</strong> {user.email}</p>
                  <p><strong>Role:</strong> {user.role}</p>
                  <p><strong>User ID:</strong> {user.id}</p>
                </>
              )}
            </div>
          </div>

          {/* Browser Info */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Browser Information</h2>
            <div className="space-y-2">
              <p><strong>Current URL:</strong> {window.location.href}</p>
              <p><strong>Cookies:</strong></p>
              <pre className="text-xs bg-gray-700 p-2 rounded overflow-x-auto">
                {document.cookie || 'No cookies found'}
              </pre>
            </div>
          </div>

          {/* API Tests */}
          <div className="bg-gray-800 p-6 rounded-lg md:col-span-2">
            <h2 className="text-xl font-semibold mb-4">API Tests</h2>
            <div className="space-y-4">
              <div>
                <button 
                  onClick={testAuthEndpoint}
                  className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded mr-4"
                >
                  Test /auth/me
                </button>
                <button 
                  onClick={testAdminEndpoint}
                  className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded"
                >
                  Test Admin Stats
                </button>
              </div>

              {/* Test Results */}
              {Object.keys(testResults).length > 0 && (
                <div className="mt-4">
                  <h3 className="text-lg font-semibold mb-2">Test Results:</h3>
                  <pre className="text-xs bg-gray-700 p-4 rounded overflow-x-auto">
                    {JSON.stringify(testResults, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="bg-gray-800 p-6 rounded-lg md:col-span-2">
            <h2 className="text-xl font-semibold mb-4">Actions</h2>
            <div className="space-x-4">
              <button 
                onClick={navigateToHome}
                className="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded"
              >
                Go to Home
              </button>
              <button 
                onClick={() => window.location.href = '/en/admin-dashboard'}
                className="bg-red-600 hover:bg-red-700 px-4 py-2 rounded"
              >
                Try Admin Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDebugPage;
