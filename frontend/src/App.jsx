import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import PropTypes from 'prop-types';
import { AuthProvider } from './context/AuthContext';
import { LanguageProvider } from './context/LanguageContext';
import { SidebarProvider } from './context/SidebarContext';
import { NotificationProvider } from './context/NotificationContext';
import { GoogleOAuthProvider } from '@react-oauth/google';
import LanguageRouter from './components/LanguageRouter';
import LanguageURLHandler from './components/LanguageURLHandler';
import NotificationContainer from './components/NotificationContainer';

import Header from './components/Header';
import Footer from './components/Footer';
import Sidebar from './components/Sidebar';
import { useSidebar } from './context/SidebarContext';

function AppContent({ children }) {
  const { isSidebarOpen, toggleSidebar } = useSidebar();

  return (
    <div className="min-h-screen bg-[#0d0d0d] text-white">
      <Header />
      <div className="flex flex-1">
        <Sidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />
        <main className={`flex-1 ${isSidebarOpen ? 'lg:pl-64' : 'lg:pl-0'} overflow-x-hidden`}>
          {children}
        </main>
      </div>
      <Footer />
      <NotificationContainer />
    </div>
  );
}

AppContent.propTypes = {
  children: PropTypes.node.isRequired
};

function App() {
  return (
    <GoogleOAuthProvider clientId={import.meta.env.VITE_GOOGLE_CLIENT_ID}>
      <Router>
        <LanguageProvider>
          <AuthProvider>
            <SidebarProvider>
              <NotificationProvider>
                <LanguageURLHandler />
                <AppContent>
                  <Routes>
                    {/* Language-specific routes */}
                    <Route path="/:lang/*" element={<LanguageRouter />} />

                    {/* Root routes without language prefix - redirect handled by LanguageURLHandler */}
                    <Route path="*" element={<LanguageRouter />} />
                  </Routes>
                </AppContent>
              </NotificationProvider>
            </SidebarProvider>
          </AuthProvider>
        </LanguageProvider>
      </Router>
    </GoogleOAuthProvider>
  );
}

export default App;
