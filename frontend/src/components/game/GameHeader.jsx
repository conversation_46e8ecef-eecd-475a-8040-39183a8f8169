import PropTypes from 'prop-types';
import StarRating from '../common/StarRating';
import ShareGame from './ShareGame';

/**
 * Component for displaying game header information: title, publisher, rating, etc.
 */
const GameHeader = ({ game, avgRating, reviewCount }) => {
  // Determine if we have platform links to show
  const hasPlatformLinks = game && (game.steam_url || game.itch_url || game.epic_games_url);
  
  return (
    <div>      
      <h1 className="text-4xl font-bold text-white mb-4">{game.title}</h1>

      {/* Share Game Component */}
      <div className="mb-6">
        <ShareGame gameTitle={game.title} />
      </div>
      
      {/* Game metadata */}
      <div className="space-y-4 mb-6">
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-gray-400">Published by:</span>
          <span className="text-orange-400 font-medium">{game.publisher_name}</span>
        </div>
        
        <div className="flex flex-wrap items-center gap-4">
          <span className="text-gray-400">User Rating:</span>
          <div className="flex items-center gap-2">
            <StarRating rating={avgRating} />
            {reviewCount > 0 ? (
              <span className="text-gray-300 text-sm">
                {avgRating} out of 5 ({reviewCount} {reviewCount === 1 ? 'review' : 'reviews'})
              </span>
            ) : (
              <span className="text-gray-500 text-sm">No ratings yet</span>
            )}
          </div>
        </div>
        
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-gray-400">Release Date:</span>
          <span className="text-gray-300">{new Date(game.release_date).toLocaleDateString()}</span>
        </div>
        
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-gray-400">Price:</span>
          <span className="text-green-400 font-semibold">{
            game.price_model === 'free' ? 'Free' :
            game.price_model === 'paid' ? `$${game.price}` :
            game.price_model === 'credits' ? `${game.credit_price} Credits` : 'Free'
          }</span>
        </div>
        
        {hasPlatformLinks && (
          <div className="flex flex-wrap items-center gap-2">
            <span className="text-gray-400">Available on:</span>
            <div className="flex gap-2">
              {game.steam_url && (
                <a href={game.steam_url} target="_blank" rel="noopener noreferrer" className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors duration-200">
                  Steam
                </a>
              )}
              {game.itch_url && (
                <a href={game.itch_url} target="_blank" rel="noopener noreferrer" className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors duration-200">
                  itch.io
                </a>
              )}
              {game.epic_games_url && (
                <a href={game.epic_games_url} target="_blank" rel="noopener noreferrer" className="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm transition-colors duration-200">
                  Epic Games
                </a>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

GameHeader.propTypes = {
  game: PropTypes.shape({
    title: PropTypes.string.isRequired,
    publisher_name: PropTypes.string,
    release_date: PropTypes.string,
    price_model: PropTypes.string,
    price: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    credit_price: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    steam_url: PropTypes.string,
    itch_url: PropTypes.string,
    epic_games_url: PropTypes.string
  }).isRequired,
  avgRating: PropTypes.number.isRequired,
  reviewCount: PropTypes.number.isRequired
};

export default GameHeader; 