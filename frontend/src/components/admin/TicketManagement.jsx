import { useState, useEffect } from 'react';
import { FaTicketAlt, FaUser, FaClock, <PERSON>aReply, FaCheck, FaTimes } from 'react-icons/fa';
import axios from 'axios';
import { API_URL } from '../../config/env';
import GameLoader from '../GameLoader';

const TicketManagement = () => {
  const [tickets, setTickets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [showTicketModal, setShowTicketModal] = useState(false);
  const [response, setResponse] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterPriority, setFilterPriority] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchTickets();
  }, [currentPage, filterStatus, filterPriority]);

  const fetchTickets = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage,
        limit: 20,
        status: filterStatus,
        priority: filterPriority
      });

      const response = await axios.get(`${API_URL}/api/tickets?${params}`, {
        withCredentials: true
      });

      setTickets(response.data.tickets);
      setTotalPages(response.data.totalPages);
    } catch (error) {
      console.error('Error fetching tickets:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTicketDetails = async (ticketId) => {
    try {
      const response = await axios.get(`${API_URL}/api/tickets/${ticketId}`, {
        withCredentials: true
      });
      setSelectedTicket(response.data);
      setShowTicketModal(true);
    } catch (error) {
      console.error('Error fetching ticket details:', error);
    }
  };

  const handleRespondToTicket = async () => {
    if (!selectedTicket || !response.trim()) return;

    try {
      await axios.post(`${API_URL}/api/tickets/${selectedTicket.id}/respond`, {
        message: response.trim()
      }, {
        withCredentials: true
      });

      setResponse('');
      fetchTicketDetails(selectedTicket.id); // Refresh ticket details
      fetchTickets(); // Refresh tickets list
    } catch (error) {
      console.error('Error responding to ticket:', error);
    }
  };

  const handleUpdateTicketStatus = async (ticketId, status) => {
    try {
      await axios.patch(`${API_URL}/api/tickets/${ticketId}/status`, {
        status
      }, {
        withCredentials: true
      });

      fetchTickets();
      if (selectedTicket && selectedTicket.id === ticketId) {
        fetchTicketDetails(ticketId);
      }
    } catch (error) {
      console.error('Error updating ticket status:', error);
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'bg-red-900 text-red-200 border-red-600';
      case 'medium': return 'bg-yellow-900 text-yellow-200 border-yellow-600';
      case 'low': return 'bg-green-900 text-green-200 border-green-600';
      default: return 'bg-gray-700 text-gray-300 border-gray-600';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'open': return 'bg-blue-900 text-blue-200 border-blue-600';
      case 'in_progress': return 'bg-yellow-900 text-yellow-200 border-yellow-600';
      case 'resolved': return 'bg-green-900 text-green-200 border-green-600';
      case 'closed': return 'bg-gray-700 text-gray-300 border-gray-600';
      default: return 'bg-gray-700 text-gray-300 border-gray-600';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <GameLoader size="lg" variant="gamepad" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-white mb-2">Ticket Management</h2>
        <p className="text-gray-400">Manage user support tickets and bug reports</p>
      </div>

      {/* Filters */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Status</option>
            <option value="open">Open</option>
            <option value="in_progress">In Progress</option>
            <option value="resolved">Resolved</option>
            <option value="closed">Closed</option>
          </select>

          <select
            value={filterPriority}
            onChange={(e) => setFilterPriority(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Priority</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>

          <button
            onClick={fetchTickets}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Tickets Table */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Ticket
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Priority
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Created
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {tickets.map((ticket) => (
                <tr key={ticket.id} className="hover:bg-gray-700">
                  <td className="px-6 py-4">
                    <div>
                      <div className="text-sm font-medium text-white">
                        #{ticket.id} - {ticket.subject}
                      </div>
                      <div className="text-sm text-gray-400 truncate max-w-xs">
                        {ticket.description}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <FaUser className="text-gray-400 mr-2" />
                      <span className="text-sm text-white">{ticket.user?.username || 'Unknown'}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getPriorityColor(ticket.priority)}`}>
                      {ticket.priority}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getStatusColor(ticket.status)}`}>
                      {ticket.status.replace('_', ' ')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                    <div className="flex items-center">
                      <FaClock className="mr-1" />
                      {new Date(ticket.created_at).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => fetchTicketDetails(ticket.id)}
                        className="text-blue-400 hover:text-blue-300 flex items-center gap-1"
                      >
                        <FaTicketAlt /> View
                      </button>
                      
                      {ticket.status === 'open' && (
                        <button
                          onClick={() => handleUpdateTicketStatus(ticket.id, 'in_progress')}
                          className="text-yellow-400 hover:text-yellow-300 flex items-center gap-1"
                        >
                          <FaClock /> Start
                        </button>
                      )}
                      
                      {ticket.status !== 'resolved' && ticket.status !== 'closed' && (
                        <button
                          onClick={() => handleUpdateTicketStatus(ticket.id, 'resolved')}
                          className="text-green-400 hover:text-green-300 flex items-center gap-1"
                        >
                          <FaCheck /> Resolve
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-400">
          Page {currentPage} of {totalPages}
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50"
          >
            Previous
          </button>
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>

      {/* Ticket Detail Modal */}
      {showTicketModal && selectedTicket && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto border border-gray-700">
            <div className="p-6">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h3 className="text-2xl font-bold text-white mb-2">
                    Ticket #{selectedTicket.id}: {selectedTicket.subject}
                  </h3>
                  <div className="flex items-center gap-4 text-sm text-gray-400">
                    <span>By: {selectedTicket.user?.username}</span>
                    <span>Created: {new Date(selectedTicket.created_at).toLocaleString()}</span>
                    <span className={`px-2 py-1 rounded-full border ${getPriorityColor(selectedTicket.priority)}`}>
                      {selectedTicket.priority} priority
                    </span>
                    <span className={`px-2 py-1 rounded-full border ${getStatusColor(selectedTicket.status)}`}>
                      {selectedTicket.status.replace('_', ' ')}
                    </span>
                  </div>
                </div>
                <button
                  onClick={() => setShowTicketModal(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <FaTimes />
                </button>
              </div>

              <div className="mb-6">
                <h4 className="text-lg font-semibold text-white mb-2">Description</h4>
                <div className="bg-gray-700 rounded-lg p-4 text-gray-300">
                  {selectedTicket.description}
                </div>
              </div>

              {/* Responses */}
              <div className="mb-6">
                <h4 className="text-lg font-semibold text-white mb-4">Responses</h4>
                <div className="space-y-4 max-h-64 overflow-y-auto">
                  {selectedTicket.responses?.map((resp) => (
                    <div key={resp.id} className="bg-gray-700 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <span className="font-medium text-white">
                          {resp.responder?.username || 'System'}
                        </span>
                        <span className="text-sm text-gray-400">
                          {new Date(resp.created_at).toLocaleString()}
                        </span>
                      </div>
                      <p className="text-gray-300">{resp.message}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Response Form */}
              <div className="space-y-4">
                <h4 className="text-lg font-semibold text-white">Add Response</h4>
                <textarea
                  value={response}
                  onChange={(e) => setResponse(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows="4"
                  placeholder="Type your response..."
                />
                <div className="flex justify-between items-center">
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleUpdateTicketStatus(selectedTicket.id, 'in_progress')}
                      className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors duration-200"
                    >
                      Mark In Progress
                    </button>
                    <button
                      onClick={() => handleUpdateTicketStatus(selectedTicket.id, 'resolved')}
                      className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200"
                    >
                      Mark Resolved
                    </button>
                  </div>
                  <button
                    onClick={handleRespondToTicket}
                    disabled={!response.trim()}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50 flex items-center gap-2"
                  >
                    <FaReply /> Send Response
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TicketManagement;
