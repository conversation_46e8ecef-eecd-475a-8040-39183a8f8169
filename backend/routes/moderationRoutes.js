const express = require('express');
const router = express.Router();
const moderationController = require('../controllers/moderationController');
const { authenticateToken } = require('../middleware/authMiddleware');
const { requireRole, requirePermission, checkBanStatus } = require('../middleware/roleMiddleware');

// Apply authentication and ban check to all routes
router.use(authenticateToken);
router.use(checkBanStatus);

// User management routes
router.post('/users/:userId/ban', 
  requirePermission('BAN_USER'), 
  moderationController.banUser
);

router.post('/users/:userId/unban', 
  requirePermission('UNBAN_USER'), 
  moderationController.unbanUser
);

router.post('/users/:userId/promote', 
  requirePermission('PROMOTE_USER'), 
  moderationController.promoteUser
);

router.get('/users/:userId/ban-status', 
  requirePermission('VIEW_USER_DETAILS'), 
  moderationController.getUserBanStatus
);

// Game management routes
router.delete('/games/:gameId', 
  requirePermission('DELETE_GAME'), 
  moderationController.deleteGame
);

router.post('/games/:gameId/restore', 
  requirePermission('RESTORE_GAME'), 
  moderationController.restoreGame
);

// Review management routes
router.delete('/reviews/:reviewId', 
  requirePermission('DELETE_REVIEW'), 
  moderationController.deleteReview
);

router.put('/reviews/:reviewId', 
  requirePermission('EDIT_REVIEW'), 
  moderationController.editReview
);

// Moderation logs and dashboard
router.get('/logs', 
  requirePermission('VIEW_MODERATION_LOGS'), 
  moderationController.getModerationLogs
);

router.get('/dashboard/stats',
  requirePermission('VIEW_ANALYTICS'),
  moderationController.getDashboardStats
);

// Additional routes for admin dashboard
router.get('/users',
  requirePermission('VIEW_USER_DETAILS'),
  moderationController.getUsers
);

router.get('/games',
  requirePermission('VIEW_GAME_DETAILS'),
  moderationController.getGames
);

router.get('/settings',
  requirePermission('VIEW_SYSTEM_SETTINGS'),
  moderationController.getSettings
);

router.put('/settings',
  requirePermission('EDIT_SYSTEM_SETTINGS'),
  moderationController.updateSettings
);

module.exports = router;
