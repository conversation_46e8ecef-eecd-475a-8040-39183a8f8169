const ModerationService = require('../services/moderation.service');
const db = require('../config/database');

/**
 * Ban a user
 */
exports.banUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const { reason, duration } = req.body; // duration in seconds, null for permanent
    const moderatorId = req.user.userId;

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({ message: 'Ban reason is required' });
    }

    // Check if target user exists
    const targetUser = await db('users').select('id', 'username', 'role').where('id', userId).first();
    if (!targetUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Prevent banning admins or users with higher/equal role
    if (targetUser.role === 'admin' || 
        (targetUser.role === 'moderator' && req.user.role !== 'admin')) {
      return res.status(403).json({ message: 'Cannot ban users with equal or higher privileges' });
    }

    const result = await ModerationService.banUser(moderatorId, userId, reason, duration);
    res.json(result);
  } catch (error) {
    console.error('Error banning user:', error);
    res.status(500).json({ message: 'Server error while banning user' });
  }
};

/**
 * Unban a user
 */
exports.unbanUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const { reason } = req.body;
    const moderatorId = req.user.userId;

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({ message: 'Unban reason is required' });
    }

    const result = await ModerationService.unbanUser(moderatorId, userId, reason);
    res.json(result);
  } catch (error) {
    console.error('Error unbanning user:', error);
    res.status(500).json({ message: 'Server error while unbanning user' });
  }
};

/**
 * Delete a game
 */
exports.deleteGame = async (req, res) => {
  try {
    const { gameId } = req.params;
    const { reason } = req.body;
    const moderatorId = req.user.userId;

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({ message: 'Deletion reason is required' });
    }

    // Check if game exists
    const game = await db('games').select('id', 'title').where('id', gameId).first();
    if (!game) {
      return res.status(404).json({ message: 'Game not found' });
    }

    const result = await ModerationService.deleteGame(moderatorId, gameId, reason);
    res.json(result);
  } catch (error) {
    console.error('Error deleting game:', error);
    res.status(500).json({ message: 'Server error while deleting game' });
  }
};

/**
 * Restore a game
 */
exports.restoreGame = async (req, res) => {
  try {
    const { gameId } = req.params;
    const { reason } = req.body;
    const moderatorId = req.user.userId;

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({ message: 'Restoration reason is required' });
    }

    const result = await ModerationService.restoreGame(moderatorId, gameId, reason);
    res.json(result);
  } catch (error) {
    console.error('Error restoring game:', error);
    res.status(500).json({ message: 'Server error while restoring game' });
  }
};

/**
 * Delete a review
 */
exports.deleteReview = async (req, res) => {
  try {
    const { reviewId } = req.params;
    const { reason } = req.body;
    const moderatorId = req.user.userId;

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({ message: 'Deletion reason is required' });
    }

    // Check if review exists
    const review = await db('reviews').select('id', 'title').where('id', reviewId).first();
    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    const result = await ModerationService.deleteReview(moderatorId, reviewId, reason);
    res.json(result);
  } catch (error) {
    console.error('Error deleting review:', error);
    res.status(500).json({ message: 'Server error while deleting review' });
  }
};

/**
 * Edit a review (admin only)
 */
exports.editReview = async (req, res) => {
  try {
    const { reviewId } = req.params;
    const { newContent, reason } = req.body;
    const moderatorId = req.user.userId;

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({ message: 'Edit reason is required' });
    }

    if (!newContent || Object.keys(newContent).length === 0) {
      return res.status(400).json({ message: 'New content is required' });
    }

    const result = await ModerationService.editReview(moderatorId, reviewId, newContent, reason);
    res.json(result);
  } catch (error) {
    console.error('Error editing review:', error);
    res.status(500).json({ message: 'Server error while editing review' });
  }
};

/**
 * Promote user role (admin only)
 */
exports.promoteUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const { newRole, reason } = req.body;
    const adminId = req.user.userId;

    if (!['user', 'moderator', 'admin'].includes(newRole)) {
      return res.status(400).json({ message: 'Invalid role specified' });
    }

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({ message: 'Promotion reason is required' });
    }

    const result = await ModerationService.promoteUser(adminId, userId, newRole, reason);
    res.json(result);
  } catch (error) {
    console.error('Error promoting user:', error);
    res.status(500).json({ message: 'Server error while promoting user' });
  }
};

/**
 * Get moderation logs
 */
exports.getModerationLogs = async (req, res) => {
  try {
    const {
      moderatorId,
      actionType,
      targetUserId,
      dateFrom,
      dateTo,
      page = 1,
      limit = 50
    } = req.query;

    const filters = {};
    if (moderatorId) filters.moderatorId = moderatorId;
    if (actionType) filters.actionType = actionType;
    if (targetUserId) filters.targetUserId = targetUserId;
    if (dateFrom) filters.dateFrom = dateFrom;
    if (dateTo) filters.dateTo = dateTo;

    const options = {
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit)
    };

    const logs = await ModerationService.getModerationLogs(filters, options);
    res.json({ logs, page: parseInt(page), limit: parseInt(limit) });
  } catch (error) {
    console.error('Error getting moderation logs:', error);
    res.status(500).json({ message: 'Server error while fetching moderation logs' });
  }
};

/**
 * Get user ban status
 */
exports.getUserBanStatus = async (req, res) => {
  try {
    const { userId } = req.params;
    const banStatus = await ModerationService.getUserBanStatus(userId);
    res.json(banStatus);
  } catch (error) {
    console.error('Error getting user ban status:', error);
    res.status(500).json({ message: 'Server error while fetching ban status' });
  }
};

/**
 * Get moderation dashboard stats
 */
exports.getDashboardStats = async (req, res) => {
  try {
    const [
      totalUsers,
      bannedUsers,
      totalGames,
      removedGames,
      totalReviews,
      openTickets,
      pendingReports
    ] = await Promise.all([
      db('users').count('id as count').first(),
      db('users').where('is_banned', true).count('id as count').first(),
      db('games').count('id as count').first(),
      db('games').where('status', 'removed').count('id as count').first(),
      db('reviews').count('id as count').first(),
      db('tickets').whereIn('status', ['open', 'in_progress']).count('id as count').first(),
      db('reports').where('status', 'pending').count('id as count').first()
    ]);

    const stats = {
      users: {
        total: parseInt(totalUsers.count),
        banned: parseInt(bannedUsers.count)
      },
      games: {
        total: parseInt(totalGames.count),
        removed: parseInt(removedGames.count)
      },
      reviews: {
        total: parseInt(totalReviews.count)
      },
      tickets: {
        open: parseInt(openTickets.count)
      },
      reports: {
        pending: parseInt(pendingReports.count)
      }
    };

    res.json(stats);
  } catch (error) {
    console.error('Error getting dashboard stats:', error);
    res.status(500).json({ message: 'Server error while fetching dashboard stats' });
  }
};
